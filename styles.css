/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: white;
    color: black;
    overflow-x: hidden;
}

/* Bento Grid Layout */
.bento-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(10, minmax(120px, auto));
    gap: 20px;
    max-width: 1920px;
    margin: 0 auto;
    padding: 40px 20px;
    min-height: 100vh;
}

/* Bento Item Base */
.bento-item {
    background: white;
    border-radius: 24px;
    padding: 32px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bento-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Grid Item Positions */
.hero {
    grid-column: 1 / 8;
    grid-row: 1 / 3;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
}

.stats {
    grid-column: 8 / 13;
    grid-row: 1 / 2;
}

.evolution {
    grid-column: 8 / 13;
    grid-row: 2 / 3;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.08) 0%, transparent 100%);
}

.skills-overview {
    grid-column: 1 / 5;
    grid-row: 3 / 5;
}

.ai-modules {
    grid-column: 5 / 9;
    grid-row: 3 / 4;
}

.transformation {
    grid-column: 9 / 13;
    grid-row: 3 / 5;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.06) 0%, transparent 100%);
}

.framework {
    grid-column: 5 / 9;
    grid-row: 4 / 6;
}

.maturity {
    grid-column: 1 / 5;
    grid-row: 5 / 7;
}

.implementation {
    grid-column: 9 / 13;
    grid-row: 5 / 6;
}

.tools {
    grid-column: 5 / 9;
    grid-row: 6 / 7;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.04) 0%, transparent 100%);
}

.impact {
    grid-column: 9 / 13;
    grid-row: 6 / 8;
}

.cta {
    grid-column: 1 / 9;
    grid-row: 7 / 8;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
}

.key-insights {
    grid-column: 9 / 13;
    grid-row: 7 / 8;
}

.author-quote {
    grid-column: 1 / 13;
    grid-row: 8 / 10;
    background: linear-gradient(135deg, rgba(77, 107, 254, 0.05) 0%, transparent 100%);
}

/* Typography */
.mega-text {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
}

.large-text {
    font-size: clamp(2rem, 4vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
}

.medium-text {
    font-size: clamp(1.5rem, 2.5vw, 2.5rem);
    font-weight: 700;
}

.small-accent {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    opacity: 0.7;
}

/* Icon Styles */
.icon-large {
    font-size: 4rem;
    opacity: 0.1;
    position: absolute;
    right: 20px;
    top: 20px;
}

/* Progress Ring */
.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    stroke: #4D6BFE;
    stroke-linecap: round;
    transition: stroke-dasharray 0.35s;
    transform: rotate(0deg);
    transform-origin: 50% 50%;
}

/* Responsive Design - Tablet */
@media (max-width: 1200px) {
    .bento-grid {
        grid-template-columns: repeat(8, 1fr);
        grid-template-rows: repeat(12, minmax(100px, auto));
        min-height: auto;
    }

    .hero { grid-column: 1 / 6; grid-row: 1 / 3; }
    .stats { grid-column: 6 / 9; grid-row: 1 / 2; }
    .evolution { grid-column: 6 / 9; grid-row: 2 / 3; }
    .skills-overview { grid-column: 1 / 5; grid-row: 3 / 5; }
    .ai-modules { grid-column: 5 / 9; grid-row: 3 / 4; }
    .transformation { grid-column: 5 / 9; grid-row: 4 / 6; }
    .framework { grid-column: 1 / 5; grid-row: 5 / 7; }
    .maturity { grid-column: 5 / 9; grid-row: 5 / 7; }
    .implementation { grid-column: 1 / 5; grid-row: 7 / 8; }
    .tools { grid-column: 5 / 9; grid-row: 7 / 8; }
    .impact { grid-column: 1 / 5; grid-row: 8 / 10; }
    .cta { grid-column: 5 / 9; grid-row: 8 / 9; }
    .key-insights { grid-column: 5 / 9; grid-row: 9 / 10; }
    .author-quote { grid-column: 1 / 9; grid-row: 10 / 12; }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .bento-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 16px;
        padding: 20px 16px;
        min-height: auto;
    }

    .bento-item {
        grid-column: 1 !important;
        grid-row: auto !important;
        padding: 24px;
    }

    .mega-text {
        font-size: clamp(2rem, 10vw, 3rem);
    }

    .large-text {
        font-size: clamp(1.5rem, 6vw, 2rem);
    }

    .medium-text {
        font-size: clamp(1.2rem, 4vw, 1.5rem);
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Ripple Effect */
@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(77, 107, 254, 0.1);
    transform: scale(0);
    animation: ripple 0.6s ease-out;
    pointer-events: none;
}

/* Button Hover Effects */
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(77, 107, 254, 0.3);
}

/* Progress Bar Animation */
.progress-bar {
    transition: width 1s ease;
}

/* Chart Container */
#frameworkChart {
    max-width: 100%;
    height: auto;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #4D6BFE;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #3d5bfe;
}

/* Focus States for Accessibility */
.bento-item:focus {
    outline: 2px solid #4D6BFE;
    outline-offset: 2px;
}

button:focus {
    outline: 2px solid #4D6BFE;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .bento-grid {
        display: block;
    }

    .bento-item {
        break-inside: avoid;
        margin-bottom: 20px;
    }

    .icon-large {
        display: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .bento-item {
        border: 2px solid #000;
    }

    .small-accent {
        opacity: 1;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .bento-item {
        transition: none;
    }

    .fade-in {
        transition: none;
    }

    .progress-ring-circle {
        transition: none;
    }
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here if needed */
}
