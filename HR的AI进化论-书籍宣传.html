<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR的AI进化论 - 书籍宣传</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.5em;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .header .author {
            font-size: 1.2em;
            opacity: 0.8;
        }

        .intro-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .intro-section h2 {
            color: #667eea;
            font-size: 2.2em;
            margin-bottom: 20px;
            text-align: center;
        }

        .intro-content {
            font-size: 1.1em;
            text-align: center;
            color: #555;
            max-width: 800px;
            margin: 0 auto;
        }

        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .highlight-box h3 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .chapters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .chapter-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chapter-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .chapter-card h3 {
            color: #667eea;
            font-size: 1.4em;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .chapter-card ul {
            list-style: none;
            padding-left: 0;
        }

        .chapter-card li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .chapter-card li:before {
            content: "▶";
            color: #667eea;
            position: absolute;
            left: 0;
        }

        .framework-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .framework-section h2 {
            color: #667eea;
            font-size: 2.2em;
            margin-bottom: 30px;
            text-align: center;
        }

        .framework-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .framework-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }

        .framework-item h4 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .framework-item p {
            color: #555;
            font-size: 0.95em;
        }

        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px;
            border-radius: 15px;
            text-align: center;
            margin: 40px 0;
        }

        .cta-section h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.3em;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 15px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .chapters-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <div class="header">
            <h1>HR的AI进化论</h1>
            <div class="subtitle">从传统管理者到AI赋能者的完整蜕变指南</div>
            <div class="author">作者：周一</div>
        </div>

        <!-- 简介部分 -->
        <div class="intro-section">
            <h2>🚀 开启HR的AI时代</h2>
            <div class="intro-content">
                <p>这是一本专为HR从业者量身定制的AI实战指南。作者周一从传统HR高管到AI学徒的转型经历，为我们揭示了HR在AI时代的全新可能性。</p>
                
                <div class="highlight-box">
                    <h3>核心价值主张</h3>
                    <p>不是教你如何使用AI工具，而是教你如何成为AI时代的HR新物种——从工具使用者进化为组织变革的总设计师</p>
                </div>
            </div>
        </div>

        <!-- 核心框架 -->
        <div class="framework-section">
            <h2>📊 "智能力"七大核心技能</h2>
            <div class="framework-grid">
                <div class="framework-item">
                    <h4>D3 · 工具选用</h4>
                    <p>像招聘一样选工具，构建你的"兵器谱"</p>
                </div>
                <div class="framework-item">
                    <h4>D4 · 提问指令</h4>
                    <p>R-STAR框架，让AI猜到你心思的艺术</p>
                </div>
                <div class="framework-item">
                    <h4>D5 · 结果判断</h4>
                    <p>审辨式交互，在AI胡言乱语中淘出真金</p>
                </div>
                <div class="framework-item">
                    <h4>D6 · 融入提效</h4>
                    <p>从单点提效到流程再造的建筑蓝图</p>
                </div>
                <div class="framework-item">
                    <h4>D7 · 知识构建</h4>
                    <p>为团队打造"外置大脑"，沉淀集体智慧</p>
                </div>
                <div class="framework-item">
                    <h4>D8 · 探索创新</h4>
                    <p>从能力复用到价值发现的未来罗盘</p>
                </div>
                <div class="framework-item">
                    <h4>D9 · 智能涌现</h4>
                    <p>零代码创造你的"AI员工"，掌握创造之力</p>
                </div>
            </div>
        </div>
