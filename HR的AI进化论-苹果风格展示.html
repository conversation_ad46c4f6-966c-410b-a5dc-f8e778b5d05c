<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR的AI进化论 - Apple Style Presentation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4D6BFE',
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

        body {
            font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(8, minmax(120px, auto));
            gap: 20px;
            max-width: 1920px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .bento-item {
            background: white;
            border-radius: 24px;
            padding: 32px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bento-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .hero {
            grid-column: 1 / 8;
            grid-row: 1 / 3;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
        }

        .stats {
            grid-column: 8 / 13;
            grid-row: 1 / 2;
        }

        .evolution {
            grid-column: 8 / 13;
            grid-row: 2 / 3;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.08) 0%, transparent 100%);
        }

        .skills-overview {
            grid-column: 1 / 5;
            grid-row: 3 / 5;
        }

        .ai-modules {
            grid-column: 5 / 9;
            grid-row: 3 / 4;
        }

        .transformation {
            grid-column: 9 / 13;
            grid-row: 3 / 5;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.06) 0%, transparent 100%);
        }

        .framework {
            grid-column: 5 / 9;
            grid-row: 4 / 6;
        }

        .maturity {
            grid-column: 1 / 5;
            grid-row: 5 / 7;
        }

        .implementation {
            grid-column: 9 / 13;
            grid-row: 5 / 6;
        }

        .tools {
            grid-column: 5 / 9;
            grid-row: 6 / 7;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.04) 0%, transparent 100%);
        }

        .impact {
            grid-column: 9 / 13;
            grid-row: 6 / 8;
        }

        .cta {
            grid-column: 1 / 9;
            grid-row: 7 / 8;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
        }

        .key-insights {
            grid-column: 9 / 13;
            grid-row: 7 / 8;
        }

        .author-quote {
            grid-column: 1 / 13;
            grid-row: 8 / 9;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.05) 0%, transparent 100%);
        }

        .mega-text {
            font-size: clamp(3rem, 8vw, 8rem);
            font-weight: 900;
            line-height: 0.9;
            letter-spacing: -0.02em;
        }

        .large-text {
            font-size: clamp(2rem, 4vw, 4rem);
            font-weight: 800;
            line-height: 1.1;
        }

        .medium-text {
            font-size: clamp(1.5rem, 2.5vw, 2.5rem);
            font-weight: 700;
        }

        .small-accent {
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            opacity: 0.7;
        }

        .icon-large {
            font-size: 4rem;
            opacity: 0.1;
            position: absolute;
            right: 20px;
            top: 20px;
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            stroke: #4D6BFE;
            stroke-linecap: round;
            transition: stroke-dasharray 0.35s;
            transform: rotate(0deg);
            transform-origin: 50% 50%;
        }

        @media (max-width: 1200px) {
            .bento-grid {
                grid-template-columns: repeat(8, 1fr);
                grid-template-rows: repeat(15, minmax(100px, auto));
            }

            .hero { grid-column: 1 / 6; grid-row: 1 / 3; }
            .stats { grid-column: 6 / 9; grid-row: 1 / 2; }
            .evolution { grid-column: 6 / 9; grid-row: 2 / 3; }
            .skills-overview { grid-column: 1 / 5; grid-row: 3 / 5; }
            .ai-modules { grid-column: 5 / 9; grid-row: 3 / 4; }
            .transformation { grid-column: 5 / 9; grid-row: 4 / 6; }
            .framework { grid-column: 1 / 5; grid-row: 5 / 7; }
            .maturity { grid-column: 5 / 9; grid-row: 5 / 7; }
            .implementation { grid-column: 1 / 5; grid-row: 7 / 8; }
            .tools { grid-column: 5 / 9; grid-row: 7 / 8; }
            .impact { grid-column: 1 / 5; grid-row: 8 / 10; }
            .cta { grid-column: 5 / 9; grid-row: 8 / 9; }
            .key-insights { grid-column: 5 / 9; grid-row: 9 / 10; }
            .author-quote { grid-column: 1 / 9; grid-row: 10 / 12; }
        }

        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: 16px;
                padding: 20px 16px;
            }

            .bento-item {
                grid-column: 1;
                grid-row: auto;
                padding: 24px;
            }
        }
    </style>
</head>
<body class="bg-white text-black">
    <div class="bento-grid">
        <!-- Hero Section -->
        <div class="bento-item hero">
            <div class="small-accent text-primary mb-4">AI-POWERED HR TRANSFORMATION</div>
            <h1 class="mega-text text-black mb-6">HR的<br>AI进化论</h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl">从传统管理者到AI赋能者的完整蜕变指南</p>
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    <i class="fas fa-user-tie text-white text-xl"></i>
                </div>
                <div>
                    <div class="font-bold text-lg">周一</div>
                    <div class="small-accent">Author & AI Transformation Expert</div>
                </div>
            </div>
            <i class="fas fa-robot icon-large text-primary"></i>
        </div>

        <!-- Stats -->
        <div class="bento-item stats">
            <div class="large-text text-primary mb-2">14</div>
            <div class="font-bold text-lg mb-2">核心章节</div>
            <div class="small-accent">COMPREHENSIVE GUIDE</div>
            <div class="mt-6">
                <div class="large-text text-black mb-2">7</div>
                <div class="font-bold text-lg mb-2">智能力技能</div>
                <div class="small-accent">CORE COMPETENCIES</div>
            </div>
        </div>

        <!-- Evolution Path -->
        <div class="bento-item evolution">
            <div class="small-accent text-primary mb-4">EVOLUTION PATH</div>
            <div class="medium-text text-black mb-4">四重境界</div>
            <div class="space-y-2">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-primary rounded-full"></div>
                    <span class="font-medium">青铜 → 白银 → 黄金 → 王者</span>
                </div>
            </div>
        </div>

        <!-- Skills Overview -->
        <div class="bento-item skills-overview">
            <div class="small-accent text-primary mb-4">CORE SKILLS</div>
            <div class="medium-text text-black mb-6">"智能力"<br>操作系统</div>
            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tools text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D3 工具选用</div>
                        <div class="small-accent">Tool Selection</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-comments text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D4 提问指令</div>
                        <div class="small-accent">Prompt Engineering</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-search text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D5 结果判断</div>
                        <div class="small-accent">Result Analysis</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Modules -->
        <div class="bento-item ai-modules">
            <div class="small-accent text-primary mb-4">AI MODULES</div>
            <div class="medium-text text-black mb-4">六大模块实战</div>
            <div class="grid grid-cols-2 gap-3">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-user-plus text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">招聘</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-graduation-cap text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">培训</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-chart-line text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">绩效</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-dollar-sign text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">薪酬</div>
                </div>
            </div>
        </div>

        <!-- Transformation Journey -->
        <div class="bento-item transformation">
            <div class="small-accent text-primary mb-4">TRANSFORMATION</div>
            <div class="medium-text text-black mb-6">组织变革<br>四步路线图</div>
            <div class="space-y-3">
                <div class="flex items-center gap-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                    <span class="font-medium">定义目标状态</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">2</div>
                    <span class="font-medium">制定行动计划</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">3</div>
                    <span class="font-medium">文化重塑</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">4</div>
                    <span class="font-medium">持续进化</span>
                </div>
            </div>
        </div>

        <!-- Framework -->
        <div class="bento-item framework">
            <div class="small-accent text-primary mb-4">R-STAR FRAMEWORK</div>
            <div class="medium-text text-black mb-6">指令框架</div>
            <div class="grid grid-cols-1 gap-4">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center">
                        <span class="text-primary font-bold text-lg">R</span>
                    </div>
                    <div>
                        <div class="font-bold">Role 角色设定</div>
                        <div class="small-accent">Define AI Role</div>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center">
                        <span class="text-primary font-bold text-lg">S</span>
                    </div>
                    <div>
                        <div class="font-bold">Situation 情景设定</div>
                        <div class="small-accent">Context Setting</div>
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center">
                        <span class="text-primary font-bold text-lg">T</span>
                    </div>
                    <div>
                        <div class="font-bold">Task 任务设定</div>
                        <div class="small-accent">Task Definition</div>
                    </div>
                </div>
            </div>
            <canvas id="frameworkChart" width="300" height="150" class="mt-6"></canvas>
        </div>

        <!-- Maturity Model -->
        <div class="bento-item maturity">
            <div class="small-accent text-primary mb-4">MATURITY MODEL</div>
            <div class="medium-text text-black mb-6">AI成熟度<br>诊断模型</div>
            <div class="relative">
                <svg class="progress-ring w-32 h-32 mx-auto" width="120" height="120">
                    <circle class="progress-ring-circle" stroke="#f0f0f0" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                    <circle class="progress-ring-circle" stroke="#4D6BFE" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                            stroke-dasharray="327" stroke-dashoffset="82"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-center">
                        <div class="large-text text-primary">L3</div>
                        <div class="small-accent">INTEGRATION</div>
                    </div>
                </div>
            </div>
            <div class="mt-6 space-y-2">
                <div class="flex justify-between items-center">
                    <span class="font-medium">战略与治理</span>
                    <div class="w-16 h-2 bg-gray-200 rounded-full">
                        <div class="w-12 h-2 bg-primary rounded-full"></div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-medium">数据可度量</span>
                    <div class="w-16 h-2 bg-gray-200 rounded-full">
                        <div class="w-10 h-2 bg-primary rounded-full"></div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="font-medium">流程协同</span>
                    <div class="w-16 h-2 bg-gray-200 rounded-full">
                        <div class="w-14 h-2 bg-primary rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation -->
        <div class="bento-item implementation">
            <div class="small-accent text-primary mb-4">IMPLEMENTATION</div>
            <div class="large-text text-black mb-4">3</div>
            <div class="font-bold text-lg mb-2">员工物种</div>
            <div class="space-y-3">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="font-medium">拥抱者</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span class="font-medium">观望者</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="font-medium">抗拒者</span>
                </div>
            </div>
        </div>

        <!-- Tools & Technologies -->
        <div class="bento-item tools">
            <div class="small-accent text-primary mb-4">AI TOOLS</div>
            <div class="medium-text text-black mb-6">实战工具库</div>
            <div class="grid grid-cols-3 gap-3">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-brain text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">豆包</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-robot text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">Kimi</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-cog text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">ChatGPT</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-chart-bar text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">飞书AI</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-image text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">Midjourney</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-file-alt text-primary text-xl mb-2"></i>
                    <div class="font-bold text-xs">WPS AI</div>
                </div>
            </div>
        </div>

        <!-- Impact & Results -->
        <div class="bento-item impact">
            <div class="small-accent text-primary mb-4">IMPACT & RESULTS</div>
            <div class="medium-text text-black mb-6">核心价值</div>
            <div class="space-y-6">
                <div>
                    <div class="large-text text-primary mb-2">10x</div>
                    <div class="font-bold">效率提升</div>
                    <div class="small-accent">EFFICIENCY BOOST</div>
                </div>
                <div>
                    <div class="large-text text-black mb-2">80%</div>
                    <div class="font-bold">重复工作自动化</div>
                    <div class="small-accent">AUTOMATION RATE</div>
                </div>
                <div>
                    <div class="large-text text-primary mb-2">1年</div>
                    <div class="font-bold">完成10年成长</div>
                    <div class="small-accent">ACCELERATED GROWTH</div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="bento-item cta">
            <div class="flex items-center justify-between h-full">
                <div>
                    <div class="small-accent text-primary mb-4">READY TO TRANSFORM?</div>
                    <div class="large-text text-black mb-4">开启你的<br>AI进化之旅</div>
                    <p class="text-lg text-gray-600 mb-6">从传统HR到AI赋能者的完整蜕变</p>
                    <button class="bg-primary text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-opacity-90 transition-all">
                        立即开始学习
                    </button>
                </div>
                <div class="hidden lg:block">
                    <i class="fas fa-rocket text-primary" style="font-size: 8rem; opacity: 0.1;"></i>
                </div>
            </div>
        </div>

        <!-- Key Insights -->
        <div class="bento-item key-insights">
            <div class="small-accent text-primary mb-4">KEY INSIGHTS</div>
            <div class="medium-text text-black mb-6">核心洞察</div>
            <div class="space-y-4">
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 bg-primary bg-opacity-20 rounded-full flex items-center justify-center mt-1">
                        <i class="fas fa-lightbulb text-primary text-xs"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">知识库+智能体</div>
                        <div class="text-xs text-gray-600">黄金组合破局</div>
                    </div>
                </div>
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 bg-primary bg-opacity-20 rounded-full flex items-center justify-center mt-1">
                        <i class="fas fa-users text-primary text-xs"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">人机协作SOP</div>
                        <div class="text-xs text-gray-600">标准作业流程</div>
                    </div>
                </div>
                <div class="flex items-start gap-3">
                    <div class="w-6 h-6 bg-primary bg-opacity-20 rounded-full flex items-center justify-center mt-1">
                        <i class="fas fa-chart-line text-primary text-xs"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">审辨式交互</div>
                        <div class="text-xs text-gray-600">破壁→深潜→重构</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Author Quote -->
        <div class="bento-item author-quote">
            <div class="flex items-center justify-between h-full">
                <div class="flex-1">
                    <div class="small-accent text-primary mb-4">AUTHOR'S MESSAGE</div>
                    <blockquote class="text-2xl font-bold text-black mb-6 leading-tight">
                        "AI，就是你手中的那把刀。它赋予了我们'跨越周期'的能力。"
                    </blockquote>
                    <div class="flex items-center gap-4">
                        <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center">
                            <i class="fas fa-quote-right text-primary text-xl"></i>
                        </div>
                        <div>
                            <div class="font-bold text-lg">周一</div>
                            <div class="small-accent">AI Transformation Expert</div>
                            <div class="text-sm text-gray-600 mt-1">从传统HR高管到AI学徒的转型实践者</div>
                        </div>
                    </div>
                </div>
                <div class="hidden xl:block ml-8">
                    <div class="text-right">
                        <div class="large-text text-primary mb-2">2024</div>
                        <div class="font-bold text-lg">AI元年</div>
                        <div class="small-accent">THE YEAR OF AI</div>
                        <div class="mt-6 space-y-2">
                            <div class="text-sm text-gray-600">✓ 完整实战指南</div>
                            <div class="text-sm text-gray-600">✓ 真实案例分析</div>
                            <div class="text-sm text-gray-600">✓ 可落地方案</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add smooth scroll and interaction effects
        document.addEventListener('DOMContentLoaded', function() {
            const bentoItems = document.querySelectorAll('.bento-item');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            bentoItems.forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(item);
            });

            // Create framework chart
            const ctx = document.getElementById('frameworkChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['Role', 'Situation', 'Task', 'Action', 'Result'],
                        datasets: [{
                            label: 'R-STAR Framework',
                            data: [90, 85, 95, 88, 92],
                            backgroundColor: 'rgba(77, 107, 254, 0.1)',
                            borderColor: '#4D6BFE',
                            borderWidth: 2,
                            pointBackgroundColor: '#4D6BFE',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 100,
                                ticks: {
                                    display: false
                                },
                                grid: {
                                    color: '#f0f0f0'
                                },
                                angleLines: {
                                    color: '#f0f0f0'
                                },
                                pointLabels: {
                                    font: {
                                        size: 11,
                                        weight: '600'
                                    },
                                    color: '#666'
                                }
                            }
                        }
                    }
                });
            }

            // Animate progress bars
            const progressBars = document.querySelectorAll('.maturity .w-16 > div');
            progressBars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease';
                    bar.style.width = bar.style.width;
                }, index * 200);
            });

            // Animate numbers
            function animateNumber(element, target, duration = 2000) {
                const start = 0;
                const increment = target / (duration / 16);
                let current = start;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current);
                }, 16);
            }

            // Animate large numbers when they come into view
            const numberElements = document.querySelectorAll('.large-text');
            numberElements.forEach(el => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const text = entry.target.textContent;
                            const number = parseInt(text);
                            if (!isNaN(number)) {
                                animateNumber(entry.target, number);
                            }
                            observer.unobserve(entry.target);
                        }
                    });
                });
                observer.observe(el);
            });

            // Add hover effects for bento items
            bentoItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Add click ripple effect
            bentoItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(77, 107, 254, 0.1);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add CSS animation for ripple
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
