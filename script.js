// Main JavaScript for HR AI Evolution Presentation

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initScrollAnimations();
    initFrameworkChart();
    initProgressBars();
    initNumberAnimations();
    initHoverEffects();
    initRippleEffects();
    initAccessibility();
});

// Scroll-triggered animations
function initScrollAnimations() {
    const bentoItems = document.querySelectorAll('.bento-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                entry.target.classList.add('visible');
            }
        });
    }, { 
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    bentoItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        item.classList.add('fade-in');
        observer.observe(item);
    });
}

// Create R-STAR Framework Chart
function initFrameworkChart() {
    const ctx = document.getElementById('frameworkChart');
    if (!ctx) return;

    try {
        new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['Role', 'Situation', 'Task', 'Action', 'Result'],
                datasets: [{
                    label: 'R-STAR Framework',
                    data: [90, 85, 95, 88, 92],
                    backgroundColor: 'rgba(77, 107, 254, 0.1)',
                    borderColor: '#4D6BFE',
                    borderWidth: 2,
                    pointBackgroundColor: '#4D6BFE',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#4D6BFE',
                        borderWidth: 1
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            display: false,
                            stepSize: 20
                        },
                        grid: {
                            color: '#f0f0f0',
                            lineWidth: 1
                        },
                        angleLines: {
                            color: '#f0f0f0',
                            lineWidth: 1
                        },
                        pointLabels: {
                            font: {
                                size: 11,
                                weight: '600',
                                family: 'Inter'
                            },
                            color: '#666',
                            padding: 10
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                }
            }
        });
    } catch (error) {
        console.warn('Chart.js not loaded or error creating chart:', error);
    }
}

// Animate progress bars
function initProgressBars() {
    const progressBars = document.querySelectorAll('.maturity .w-16 > div');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBars = entry.target.querySelectorAll('.w-16 > div');
                progressBars.forEach((bar, index) => {
                    setTimeout(() => {
                        bar.style.transition = 'width 1s ease';
                        bar.classList.add('progress-bar');
                        // Trigger reflow to ensure animation
                        bar.offsetWidth;
                    }, index * 200);
                });
                observer.unobserve(entry.target);
            }
        });
    });

    const maturitySection = document.querySelector('.maturity');
    if (maturitySection) {
        observer.observe(maturitySection);
    }
}

// Animate numbers when they come into view
function initNumberAnimations() {
    function animateNumber(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Handle different number formats
            if (element.textContent.includes('x')) {
                element.textContent = Math.floor(current) + 'x';
            } else if (element.textContent.includes('%')) {
                element.textContent = Math.floor(current) + '%';
            } else if (element.textContent.includes('年')) {
                element.textContent = Math.floor(current) + '年';
            } else {
                element.textContent = Math.floor(current);
            }
        }, 16);
    }

    const numberElements = document.querySelectorAll('.large-text');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const text = entry.target.textContent;
                const number = parseInt(text.replace(/[^\d]/g, ''));
                
                if (!isNaN(number) && number > 0) {
                    // Reset text content before animation
                    entry.target.textContent = '0';
                    setTimeout(() => {
                        animateNumber(entry.target, number);
                    }, 300);
                }
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    numberElements.forEach(el => {
        observer.observe(el);
    });
}

// Add hover effects for bento items
function initHoverEffects() {
    const bentoItems = document.querySelectorAll('.bento-item');
    
    bentoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                this.style.transform = 'translateY(-8px) scale(1.02)';
                this.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.1)';
            }
        });
    });
}

// Add click ripple effect
function initRippleEffects() {
    const bentoItems = document.querySelectorAll('.bento-item');
    
    bentoItems.forEach(item => {
        item.addEventListener('click', function(e) {
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return;
            }
            
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.className = 'ripple';
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(77, 107, 254, 0.1);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 600);
        });
    });
}

// Accessibility enhancements
function initAccessibility() {
    // Add keyboard navigation
    const bentoItems = document.querySelectorAll('.bento-item');
    
    bentoItems.forEach((item, index) => {
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'article');
        
        // Add aria-label for screen readers
        const title = item.querySelector('.medium-text, .large-text');
        if (title) {
            item.setAttribute('aria-label', title.textContent);
        }
        
        // Keyboard navigation
        item.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // Add skip link for keyboard users
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-white px-4 py-2 rounded';
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Add main content landmark
    const bentoGrid = document.querySelector('.bento-grid');
    if (bentoGrid) {
        bentoGrid.setAttribute('id', 'main-content');
        bentoGrid.setAttribute('role', 'main');
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Handle window resize
window.addEventListener('resize', debounce(() => {
    // Recalculate any size-dependent elements
    const chart = Chart.getChart('frameworkChart');
    if (chart) {
        chart.resize();
    }
}, 250));

// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
    });
}

// Error handling
window.addEventListener('error', (e) => {
    console.error('JavaScript error:', e.error);
});

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initScrollAnimations,
        initFrameworkChart,
        initProgressBars,
        initNumberAnimations,
        initHoverEffects,
        initRippleEffects,
        initAccessibility
    };
}
