<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR的AI进化论 - Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4D6BFE',
                    }
                }
            }
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: white;
            color: black;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(auto-fit, minmax(120px, auto));
            gap: 20px;
            max-width: 1920px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .bento-item {
            background: white;
            border-radius: 24px;
            padding: 32px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .hero {
            grid-column: 1 / 8;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
        }
        
        .stats {
            grid-column: 8 / 13;
            grid-row: span 1;
        }
        
        .evolution {
            grid-column: 8 / 13;
            grid-row: span 1;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.08) 0%, transparent 100%);
        }
        
        .skills-overview {
            grid-column: 1 / 5;
            grid-row: span 2;
        }
        
        .ai-modules {
            grid-column: 5 / 9;
            grid-row: span 1;
        }
        
        .transformation {
            grid-column: 9 / 13;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.06) 0%, transparent 100%);
        }
        
        .framework {
            grid-column: 5 / 9;
            grid-row: span 2;
        }
        
        .maturity {
            grid-column: 1 / 5;
            grid-row: span 2;
        }
        
        .implementation {
            grid-column: 9 / 13;
            grid-row: span 1;
        }
        
        .tools {
            grid-column: 5 / 9;
            grid-row: span 1;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.04) 0%, transparent 100%);
        }
        
        .impact {
            grid-column: 9 / 13;
            grid-row: span 2;
        }
        
        .cta {
            grid-column: 1 / 9;
            grid-row: span 1;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.1) 0%, rgba(77, 107, 254, 0.05) 100%);
        }
        
        .key-insights {
            grid-column: 9 / 13;
            grid-row: span 1;
        }
        
        .author-quote {
            grid-column: 1 / 13;
            grid-row: span 2;
            background: linear-gradient(135deg, rgba(77, 107, 254, 0.05) 0%, transparent 100%);
        }
        
        .mega-text {
            font-size: clamp(3rem, 8vw, 8rem);
            font-weight: 900;
            line-height: 0.9;
            letter-spacing: -0.02em;
        }
        
        .large-text {
            font-size: clamp(2rem, 4vw, 4rem);
            font-weight: 800;
            line-height: 1.1;
        }
        
        .medium-text {
            font-size: clamp(1.5rem, 2.5vw, 2.5rem);
            font-weight: 700;
        }
        
        .small-accent {
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            opacity: 0.7;
        }
        
        .icon-large {
            font-size: 4rem;
            opacity: 0.1;
            position: absolute;
            right: 20px;
            top: 20px;
        }
        
        @media (max-width: 1200px) {
            .bento-grid {
                grid-template-columns: repeat(8, 1fr);
            }
            
            .hero { grid-column: 1 / 6; }
            .stats { grid-column: 6 / 9; }
            .evolution { grid-column: 6 / 9; }
            .skills-overview { grid-column: 1 / 5; }
            .ai-modules { grid-column: 5 / 9; }
            .transformation { grid-column: 5 / 9; }
            .framework { grid-column: 1 / 5; }
            .maturity { grid-column: 5 / 9; }
            .implementation { grid-column: 1 / 5; }
            .tools { grid-column: 5 / 9; }
            .impact { grid-column: 1 / 5; }
            .cta { grid-column: 5 / 9; }
            .key-insights { grid-column: 5 / 9; }
            .author-quote { grid-column: 1 / 9; }
        }
        
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr;
                gap: 16px;
                padding: 20px 16px;
            }
            
            .bento-item {
                grid-column: 1 !important;
                padding: 24px;
            }
        }
    </style>
</head>
<body class="bg-white text-black">
    <div class="bento-grid">
        <!-- Hero Section -->
        <div class="bento-item hero">
            <div class="small-accent text-primary mb-4">AI-POWERED HR TRANSFORMATION</div>
            <h1 class="mega-text text-black mb-6">HR的<br>AI进化论</h1>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl">从传统管理者到AI赋能者的完整蜕变指南</p>
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    <i class="fas fa-user-tie text-white text-xl"></i>
                </div>
                <div>
                    <div class="font-bold text-lg">周一</div>
                    <div class="small-accent">Author & AI Transformation Expert</div>
                </div>
            </div>
            <i class="fas fa-robot icon-large text-primary"></i>
        </div>

        <!-- Stats -->
        <div class="bento-item stats">
            <div class="large-text text-primary mb-2">14</div>
            <div class="font-bold text-lg mb-2">核心章节</div>
            <div class="small-accent">COMPREHENSIVE GUIDE</div>
            <div class="mt-6">
                <div class="large-text text-black mb-2">7</div>
                <div class="font-bold text-lg mb-2">智能力技能</div>
                <div class="small-accent">CORE COMPETENCIES</div>
            </div>
        </div>

        <!-- Evolution Path -->
        <div class="bento-item evolution">
            <div class="small-accent text-primary mb-4">EVOLUTION PATH</div>
            <div class="medium-text text-black mb-4">四重境界</div>
            <div class="space-y-2">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-primary rounded-full"></div>
                    <span class="font-medium">青铜 → 白银 → 黄金 → 王者</span>
                </div>
            </div>
        </div>

        <!-- Skills Overview -->
        <div class="bento-item skills-overview">
            <div class="small-accent text-primary mb-4">CORE SKILLS</div>
            <div class="medium-text text-black mb-6">"智能力"<br>操作系统</div>
            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tools text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D3 工具选用</div>
                        <div class="small-accent">Tool Selection</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-comments text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D4 提问指令</div>
                        <div class="small-accent">Prompt Engineering</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-primary bg-opacity-10 rounded-lg flex items-center justify-center">
                        <i class="fas fa-search text-primary text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold">D5 结果判断</div>
                        <div class="small-accent">Result Analysis</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Modules -->
        <div class="bento-item ai-modules">
            <div class="small-accent text-primary mb-4">AI MODULES</div>
            <div class="medium-text text-black mb-4">六大模块实战</div>
            <div class="grid grid-cols-2 gap-3">
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-user-plus text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">招聘</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-graduation-cap text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">培训</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-chart-line text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">绩效</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded-lg">
                    <i class="fas fa-dollar-sign text-primary text-xl mb-2"></i>
                    <div class="font-bold text-sm">薪酬</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
